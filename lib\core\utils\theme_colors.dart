// import 'package:flutter/material.dart';
// import 'package:get/get.dart';

// import 'app_colors.dart';

// /// Helper class to get theme-aware colors
// class ThemeColors {
//   /// Background color based on theme
//   static Color get background =>
//       Get.isDarkMode ? AppColors.darkBackground : AppColors.background;

//   /// Primary color based on theme
//   static Color get primary =>
//       Get.isDarkMode ? AppColors.darkPrimary : AppColors.primaryColor;

//   /// Secondary color based on theme
//   static Color get secondary =>
//       Get.isDarkMode ? AppColors.darkSecondary : AppColors.secondaryColor;

//   /// Text color based on theme
//   static Color get text =>
//       Get.isDarkMode ? AppColors.darkText : AppColors.black;

//   /// Card color based on theme
//   static Color get card =>
//       Get.isDarkMode ? AppColors.darkCard : AppColors.white;

//   /// Divider color based on theme
//   static Color get divider =>
//       Get.isDarkMode ? AppColors.darkDivider : AppColors.secondry;

//   /// Surface color based on theme
//   static Color get surface =>
//       Get.isDarkMode ? AppColors.darkSurface : AppColors.white;

//   /// Error color based on theme
//   static Color get error =>
//       Get.isDarkMode ? AppColors.darkError : AppColors.error;

//   /// Table background color based on theme
//   static Color get tableBackground => Get.isDarkMode
//       ? AppColors.darkTableBackground
//       : AppColors.tablesBackground;

//   /// Accent color based on theme
//   static Color get accent =>
//       Get.isDarkMode ? AppColors.darkAccent : AppColors.primaryWithOpacity;

//   /// Grey color based on theme
//   static Color get grey => Get.isDarkMode ? AppColors.darkGray : AppColors.grey;
// }
