{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\POS CUBE\\android\\app\\.cxx\\RelWithDebInfo\\2x141p14\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\POS CUBE\\android\\app\\.cxx\\RelWithDebInfo\\2x141p14\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}