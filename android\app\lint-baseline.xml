<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.1.0" type="baseline" client="gradle" dependencies="true" name="AGP (8.1.0)" variant="fatal" version="8.1.0">

    <issue
        id="MissingDefaultResource"
        message="The drawable &quot;branding&quot; in drawable-night-hdpi has no declaration in the base `drawable` folder or in a `drawable-`*density*`dpi` folder; this can lead to crashes when the drawable is queried in a configuration that does not match this qualifier">
        <location
            file="src/main/res/drawable-night-hdpi/branding.png"/>
    </issue>

    <issue
        id="MissingDefaultResource"
        message="The drawable &quot;branding&quot; in drawable-night-mdpi has no declaration in the base `drawable` folder or in a `drawable-`*density*`dpi` folder; this can lead to crashes when the drawable is queried in a configuration that does not match this qualifier">
        <location
            file="src/main/res/drawable-night-mdpi/branding.png"/>
    </issue>

    <issue
        id="MissingDefaultResource"
        message="The drawable &quot;branding&quot; in drawable-night-xhdpi has no declaration in the base `drawable` folder or in a `drawable-`*density*`dpi` folder; this can lead to crashes when the drawable is queried in a configuration that does not match this qualifier">
        <location
            file="src/main/res/drawable-night-xhdpi/branding.png"/>
    </issue>

    <issue
        id="MissingDefaultResource"
        message="The drawable &quot;branding&quot; in drawable-night-xxhdpi has no declaration in the base `drawable` folder or in a `drawable-`*density*`dpi` folder; this can lead to crashes when the drawable is queried in a configuration that does not match this qualifier">
        <location
            file="src/main/res/drawable-night-xxhdpi/branding.png"/>
    </issue>

    <issue
        id="MissingDefaultResource"
        message="The drawable &quot;branding&quot; in drawable-night-xxxhdpi has no declaration in the base `drawable` folder or in a `drawable-`*density*`dpi` folder; this can lead to crashes when the drawable is queried in a configuration that does not match this qualifier">
        <location
            file="src/main/res/drawable-night-xxxhdpi/branding.png"/>
    </issue>

</issues>
