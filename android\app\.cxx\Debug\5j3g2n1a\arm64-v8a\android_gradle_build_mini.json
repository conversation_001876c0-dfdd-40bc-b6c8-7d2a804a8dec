{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\point-of-sale-flutter-1-create-a-new-project-named-point-of-sale-flutter-and-push\\android\\app\\.cxx\\Debug\\5j3g2n1a\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\point-of-sale-flutter-1-create-a-new-project-named-point-of-sale-flutter-and-push\\android\\app\\.cxx\\Debug\\5j3g2n1a\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}