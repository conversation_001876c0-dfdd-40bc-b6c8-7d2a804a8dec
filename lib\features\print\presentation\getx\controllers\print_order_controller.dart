import 'dart:io';

import 'package:esc_pos_utils_plus/esc_pos_utils_plus.dart' as esc_plus;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_esc_pos_network/flutter_esc_pos_network.dart';
import 'package:get/get.dart';
import 'package:image/image.dart' as img;
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:point_of_sale/core/widgets/failed_snack_bar.dart';
import 'package:point_of_sale/core/widgets/success_snack_bar.dart';
import 'package:point_of_sale/features/print/presentation/getx/controllers/print_controller.dart';
import 'package:point_of_sale/features/print/presentation/views/print_view.dart';
import 'package:point_of_sale/features/settings/presentation/getx/controllers/printers_ip_address_controller.dart';
import 'package:printing/printing.dart';
import 'package:screenshot/screenshot.dart';

import '../../../../../core/services/cash_data_source.dart';

class PrintOrderController extends GetxController {
  final ScreenshotController screenshotController = ScreenshotController();

  Future<Uint8List> _fetchLogoBytes(String url) async {
    try {
      final File file = await DefaultCacheManager().getSingleFile(url);
      final Uint8List bytes = await file.readAsBytes();
      return bytes;
    } catch (err) {
      throw Exception('Failed to load logo from network: $err');
    }
  }

  /// Helper function to process Arabic text for proper RTL display
  String _processArabicText(String text) {
    if (text.isEmpty) return text;

    // For now, return text as-is. The esc_pos_utils_plus should handle Arabic text
    // We can enhance this later with proper bidi processing if needed
    return text;
  }

  /// Generate ESC/POS commands for invoice printing using esc_pos_utils_plus
  Future<List<int>> _generateInvoiceEscPos(PrintController controller) async {
    final cashDataSource = Get.find<CashDataSource>();
    final invoiceData = cashDataSource.getInvoiceData();

    final logoUrl = invoiceData['logo'] as String;
    final commerce = invoiceData['commerce'] as String;
    final cashierName = invoiceData['cashierName'] as String;
    final restaurantName = invoiceData['restaurantName'] as String;
    final vatNumber = invoiceData['vatNumber'] as String;
    final vat = invoiceData['vat'] as String;
    final mainAddress = invoiceData['mainAddress'] as String;
    final websiteUrl = invoiceData['websiteUrl'] as String;
    final invoiceNumber = controller.orderNumber.toString();

    // Initialize generator with 80mm paper size
    final profile = await esc_plus.CapabilityProfile.load();
    final generator = esc_plus.Generator(esc_plus.PaperSize.mm80, profile);
    List<int> bytes = [];

    // Add logo if available
    if (logoUrl.isNotEmpty) {
      try {
        final logoBytes = await _fetchLogoBytes(logoUrl);
        final logoImage = img.decodeImage(logoBytes);
        if (logoImage != null) {
          // Resize logo to fit thermal printer width (max 576 pixels for 80mm)
          final resizedLogo =
              img.copyResize(logoImage, width: 300, height: 150);
          bytes +=
              generator.image(resizedLogo, align: esc_plus.PosAlign.center);
          bytes += generator.emptyLines(1);
        }
      } catch (e) {
        Get.log('Failed to load logo: $e');
      }
    }

    // Restaurant name and invoice title
    bytes += generator._(
      restaurantName,
      styles: esc_plus.PosStyles(
        align: esc_plus.PosAlign.center,
        height: esc_plus.PosTextSize.size2,
        width: esc_plus.PosTextSize.size2,
        bold: true,
      ),
    );

    bytes += generator.emptyLines(1);

    // Arabic invoice title
    bytes += generator.text(
      _processArabicText('فاتورة ضريبية مبسطة'),
      styles: esc_plus.PosStyles(
        align: esc_plus.PosAlign.center,
        height: esc_plus.PosTextSize.size2,
        width: esc_plus.PosTextSize.size1,
        bold: true,
      ),
    );

    // English invoice title
    bytes += generator.text(
      'Simplified Tax Invoice',
      styles: esc_plus.PosStyles(
        align: esc_plus.PosAlign.center,
        height: esc_plus.PosTextSize.size1,
        width: esc_plus.PosTextSize.size1,
        bold: true,
      ),
    );

    bytes += generator.emptyLines(1);

    // Invoice number
    bytes += generator.text(
      'Order #${invoiceNumber.trim()}',
      styles: esc_plus.PosStyles(
        align: esc_plus.PosAlign.center,
        height: esc_plus.PosTextSize.size2,
        width: esc_plus.PosTextSize.size2,
        bold: true,
      ),
    );

    bytes += generator.emptyLines(1);
    bytes += generator.hr();

    // Company Info Table
    bytes += generator.row([
      esc_plus.PosColumn(
          text: 'VAT',
          width: 3,
          styles: esc_plus.PosStyles(align: esc_plus.PosAlign.left)),
      esc_plus.PosColumn(
          text: vatNumber,
          width: 6,
          styles:
              esc_plus.PosStyles(align: esc_plus.PosAlign.center, bold: true)),
      esc_plus.PosColumn(
          text: _processArabicText('الرقم الضريبي'),
          width: 3,
          styles: esc_plus.PosStyles(align: esc_plus.PosAlign.right)),
    ]);

    bytes += generator.row([
      esc_plus.PosColumn(
          text: 'C.R',
          width: 3,
          styles: esc_plus.PosStyles(align: esc_plus.PosAlign.left)),
      esc_plus.PosColumn(
          text: commerce,
          width: 6,
          styles:
              esc_plus.PosStyles(align: esc_plus.PosAlign.center, bold: true)),
      esc_plus.PosColumn(
          text: _processArabicText('السجل التجاري'),
          width: 3,
          styles: esc_plus.PosStyles(align: esc_plus.PosAlign.right)),
    ]);

    bytes += generator.row([
      esc_plus.PosColumn(
          text: 'POS',
          width: 3,
          styles: esc_plus.PosStyles(align: esc_plus.PosAlign.left)),
      esc_plus.PosColumn(
          text: mainAddress,
          width: 6,
          styles:
              esc_plus.PosStyles(align: esc_plus.PosAlign.center, bold: true)),
      esc_plus.PosColumn(
          text: _processArabicText('نقطة البيع'),
          width: 3,
          styles: esc_plus.PosStyles(align: esc_plus.PosAlign.right)),
    ]);

    bytes += generator.row([
      esc_plus.PosColumn(
          text: 'Cashier',
          width: 3,
          styles: esc_plus.PosStyles(align: esc_plus.PosAlign.left)),
      esc_plus.PosColumn(
          text: cashierName,
          width: 6,
          styles:
              esc_plus.PosStyles(align: esc_plus.PosAlign.center, bold: true)),
      esc_plus.PosColumn(
          text: _processArabicText('الكاشير'),
          width: 3,
          styles: esc_plus.PosStyles(align: esc_plus.PosAlign.right)),
    ]);

    bytes += generator.row([
      esc_plus.PosColumn(
          text: 'Date',
          width: 3,
          styles: esc_plus.PosStyles(align: esc_plus.PosAlign.left)),
      esc_plus.PosColumn(
          text: DateFormat('yyyy/MM/dd hh:mm a').format(DateTime.now()),
          width: 6,
          styles:
              esc_plus.PosStyles(align: esc_plus.PosAlign.center, bold: true)),
      esc_plus.PosColumn(
          text: _processArabicText('التاريخ'),
          width: 3,
          styles: esc_plus.PosStyles(align: esc_plus.PosAlign.right)),
    ]);

    bytes += generator.hr();

    // Column Headers
    bytes += generator.row([
      esc_plus.PosColumn(
          text: 'Qty',
          width: 2,
          styles:
              esc_plus.PosStyles(align: esc_plus.PosAlign.left, bold: true)),
      esc_plus.PosColumn(
          text: 'Item',
          width: 6,
          styles:
              esc_plus.PosStyles(align: esc_plus.PosAlign.center, bold: true)),
      esc_plus.PosColumn(
          text: 'Price',
          width: 4,
          styles:
              esc_plus.PosStyles(align: esc_plus.PosAlign.right, bold: true)),
    ]);

    bytes += generator.row([
      esc_plus.PosColumn(
          text: _processArabicText('الكمية'),
          width: 2,
          styles: esc_plus.PosStyles(align: esc_plus.PosAlign.left)),
      esc_plus.PosColumn(
          text: _processArabicText('الصنف'),
          width: 6,
          styles: esc_plus.PosStyles(align: esc_plus.PosAlign.center)),
      esc_plus.PosColumn(
          text: _processArabicText('السعر'),
          width: 4,
          styles: esc_plus.PosStyles(align: esc_plus.PosAlign.right)),
    ]);

    bytes += generator.hr();

    // Order Items
    final items =
        controller.orderDetailsController.orderDetailsModel.data?.items ?? [];
    for (final item in items) {
      bytes += generator.row([
        esc_plus.PosColumn(
            text: item.qty ?? '0',
            width: 2,
            styles: esc_plus.PosStyles(align: esc_plus.PosAlign.left)),
        esc_plus.PosColumn(
            text: item.productName ?? '',
            width: 6,
            styles: esc_plus.PosStyles(align: esc_plus.PosAlign.center)),
        esc_plus.PosColumn(
            text: '${item.totalPrice ?? '0.00'} SR',
            width: 4,
            styles: esc_plus.PosStyles(align: esc_plus.PosAlign.right)),
      ]);
    }

    bytes += generator.hr();

    // Totals
    final subtotal = controller.totalPrice;
    final vatAmount = subtotal * (double.parse(vat) / 100);
    final totalIncludingVat = subtotal + vatAmount;

    bytes += generator.row([
      esc_plus.PosColumn(
          text: _processArabicText('المجموع بدون الضريبة - Subtotal'),
          width: 8,
          styles: esc_plus.PosStyles(align: esc_plus.PosAlign.left)),
      esc_plus.PosColumn(
          text: '${subtotal.toStringAsFixed(2)} SR',
          width: 4,
          styles:
              esc_plus.PosStyles(align: esc_plus.PosAlign.right, bold: true)),
    ]);

    bytes += generator.row([
      esc_plus.PosColumn(
          text: _processArabicText('%($vat) الضريبة - VAT'),
          width: 8,
          styles: esc_plus.PosStyles(align: esc_plus.PosAlign.left)),
      esc_plus.PosColumn(
          text: '${vatAmount.toStringAsFixed(2)} SR',
          width: 4,
          styles:
              esc_plus.PosStyles(align: esc_plus.PosAlign.right, bold: true)),
    ]);

    bytes += generator.row([
      esc_plus.PosColumn(
          text:
              _processArabicText('الإجمالي شامل الضريبة - Total including VAT'),
          width: 8,
          styles:
              esc_plus.PosStyles(align: esc_plus.PosAlign.left, bold: true)),
      esc_plus.PosColumn(
          text: '${totalIncludingVat.toStringAsFixed(2)} SR',
          width: 4,
          styles:
              esc_plus.PosStyles(align: esc_plus.PosAlign.right, bold: true)),
    ]);

    bytes += generator.hr();

    // Payment Methods
    final paymentTransactions = controller.orderDetailsController
            .orderDetailsModel.data?.paymentTransactions ??
        [];
    for (final transaction in paymentTransactions) {
      final paymentMethodName =
          '${transaction.paymentMethod?.description?[1].name ?? ''} ${transaction.paymentMethod?.description?[0].name ?? ''}';
      bytes += generator.row([
        esc_plus.PosColumn(
            text: paymentMethodName,
            width: 8,
            styles: esc_plus.PosStyles(align: esc_plus.PosAlign.left)),
        esc_plus.PosColumn(
            text: '${transaction.amount ?? ''} SR',
            width: 4,
            styles:
                esc_plus.PosStyles(align: esc_plus.PosAlign.right, bold: true)),
      ]);
    }

    bytes += generator.emptyLines(2);

    // QR Code
    if (websiteUrl.isNotEmpty) {
      bytes += generator.qrcode(websiteUrl,
          align: esc_plus.PosAlign.center, size: esc_plus.QRSize.size8);
      bytes += generator.emptyLines(1);
    }

    // Footer
    bytes += generator.text(
      'Powered by $websiteUrl',
      styles: esc_plus.PosStyles(align: esc_plus.PosAlign.center),
    );

    bytes += generator.emptyLines(3);
    bytes += generator.cut();

    return bytes;
  }

  Future<void> printInvoice(PrintController controller) async {
    if (kIsWeb) {
      await _printInvoiceWeb(controller);
      return;
    }
    await _printInvoiceMobile(controller);
  }

  Future<void> _printInvoiceWeb(PrintController controller) async {
    try {
      Get.log('Printing invoice on web platform using image capture');
      await _printInvoiceAsImagePdf(controller);
      successSnackBar(
        'Invoice ready for printing. Please use your browser\'s print dialog.',
      );
    } catch (e) {
      Get.log('Web print error: $e');
      failedSnaskBar(
        'Failed to print invoice: $e',
      );
    }
  }

  Future<void> _printInvoiceAsImagePdf(PrintController controller) async {
    try {
      Get.log('Capturing invoice widget as image for PDF');
      final invoiceWidget = Invoice(controller: controller);

      // PDF printing requires higher resolution for clarity on larger formats.
      // Target width of 1152 (576 * 2) and pixelRatio 6.0 provides good detail for A4.
      final imageBytes = await _captureWidgetAsImage(invoiceWidget,
          targetWidthPixels: 1152, pixelRatio: 6.0);

      final pdfBytes = await _createPdfFromImage(imageBytes);

      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdfBytes,
        name: 'Invoice_${controller.orderNumber}',
        format: PdfPageFormat.a4,
        usePrinterSettings: true,
      );

      Get.log('Image-based PDF print dialog opened successfully');
    } catch (e) {
      Get.log('Image-based PDF printing error: $e');
      rethrow;
    }
  }

  /// Capture a Flutter widget as an image using screenshot package
  Future<Uint8List> _captureWidgetAsImage(
    Widget widget, {
    required int targetWidthPixels,
    double pixelRatio = 3.0,
  }) async {
    try {
      Get.log('Starting widget capture using screenshot package');

      final captureContextWidget = MaterialApp(
        debugShowCheckedModeBanner: false,
        home: Scaffold(
          backgroundColor: Colors.white,
          body: SingleChildScrollView(
            // Use Builder to get a context for MediaQuery if needed within the child,
            // though for simple width enforcement, it's not strictly necessary.
            child: Builder(
              builder: (context) {
                return Container(
                  width: targetWidthPixels
                      .toDouble(), // This is the explicit width for the captured image
                  // IMPORTANT: Reduce or remove padding here if your Invoice widget
                  // already handles its own internal margins, or if you want
                  // the content to fill more of the 576px width.
                  // For a 576px wide canvas, a padding of 48px on each side
                  // leaves only (576 - 96) = 480px for the content.
                  padding: const EdgeInsets.symmetric(
                      horizontal: 8.0,
                      vertical: 4.0), // Start with minimal padding
                  color: Colors.white,
                  child: widget, // The actual invoice widget
                );
              },
            ),
          ),
        ),
      );

      final imageBytes = await screenshotController.captureFromWidget(
        captureContextWidget,
        delay: const Duration(milliseconds: 100),
        pixelRatio: pixelRatio,
        context: Get.context!, // Ensure a valid BuildContext is passed
      );

      Get.log(
          'Widget capture completed successfully - ${imageBytes.length} bytes');
      return imageBytes;
    } catch (e) {
      Get.log('Error capturing widget as image: $e');
      rethrow;
    }
  }

  Future<Uint8List> _createPdfFromImage(Uint8List imageBytes) async {
    final pdf = pw.Document();
    final image = pw.MemoryImage(imageBytes);

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(10),
        build: (pw.Context context) {
          return pw.Center(
            child: pw.Image(
              image,
              fit: pw.BoxFit.contain,
            ),
          );
        },
      ),
    );
    return pdf.save();
  }

  Future<void> _printToSinglePrinter(String printerIP, List<int> bytes) async {
    try {
      Get.log('Printing to $printerIP');
      const port = 9100;
      final printer = PrinterNetworkManager(printerIP, port: port);
      final result = await printer.printTicket(bytes);
      if (result == PosPrintResult.success) {
        Get.log('Successfully printed to $printerIP');
      } else {
        throw Exception('Failed to print to printer $printerIP: $result');
      }
    } catch (e) {
      Get.log('Error printing to $printerIP: $e');
      rethrow;
    }
  }

  Future<void> _printInvoiceMobile(PrintController controller) async {
    final printersController = Get.find<PrintersIPAddressController>();
    final printerIPs = printersController.getAllPrinterIPs();

    if (printerIPs.isEmpty) {
      Get.snackbar(
        'Error'.tr,
        'No printers configured. Please add printer IPs in settings.'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
      return;
    }

    Get.log(
        'Starting ESC/POS print job to ${printerIPs.length} printer(s) using programmatic invoice generation: $printerIPs');

    try {
      // Generate invoice using ESC/POS commands instead of image capture
      final List<int> escPosBytes = await _generateInvoiceEscPos(controller);

      Get.log(
          'Generated ${escPosBytes.length} bytes from programmatic invoice for thermal printer');

      final List<Future<void>> printTasks = [];
      for (String printerIP in printerIPs) {
        printTasks.add(_printToSinglePrinter(printerIP, escPosBytes));
      }

      try {
        await Future.wait(printTasks);
        Get.snackbar(
          'Success'.tr,
          'Invoice printed to ${printerIPs.length} printer(s) successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Get.theme.colorScheme.primary,
          colorText: Get.theme.colorScheme.onPrimary,
        );
      } catch (e) {
        Get.snackbar(
          'Warning'.tr,
          'Some printers failed. Check printer connections.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Get.theme.colorScheme.error,
          colorText: Get.theme.colorScheme.onError,
        );
      }
    } catch (e) {
      Get.log('Mobile print error: $e');
      Get.snackbar(
        'Error',
        'Failed to print invoice: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
    }
  }
}
