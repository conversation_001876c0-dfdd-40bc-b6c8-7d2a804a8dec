// import 'package:flutter/material.dart';
// import 'package:point_of_sale/core/utils/app_colors.dart';
// import 'package:point_of_sale/core/utils/size_utils.dart';

// import 'dynamic_colors.dart';

// /// Theme-aware text styles
// class ThemeTextStyle {
//   static const _fontFamily = 'Cairo';

//   /// Primary text style that adapts to theme
//   static TextStyle primary16(
//       {FontWeight weight = FontWeight.normal, Color? customColor}) {
//     return TextStyle(
//       fontSize: getFontSize(16),
//       fontWeight: weight,
//       color: customColor ?? DynamicColors.primary,
//       fontFamily: _fontFamily,
//     );
//   }

//   /// Text style for headings
//   static TextStyle heading(
//       {double size = 20,
//       FontWeight weight = FontWeight.bold,
//       Color? customColor}) {
//     return TextStyle(
//       fontSize: getFontSize(size),
//       fontWeight: weight,
//       color: customColor ?? DynamicColors.text,
//       fontFamily: _fontFamily,
//     );
//   }

//   /// Text style for body text
//   static TextStyle body(
//       {double size = 14,
//       FontWeight weight = FontWeight.normal,
//       Color? customColor}) {
//     return TextStyle(
//       fontSize: getFontSize(size),
//       fontWeight: weight,
//       color: customColor ?? DynamicColors.text,
//       fontFamily: _fontFamily,
//     );
//   }

//   /// Text style for subtitle text
//   static TextStyle subtitle(
//       {double size = 12,
//       FontWeight weight = FontWeight.w600,
//       Color? customColor}) {
//     return TextStyle(
//       fontSize: getFontSize(size),
//       fontWeight: weight,
//       color: customColor ?? AppColors.secondry,
//       fontFamily: _fontFamily,
//     );
//   }

//   /// Text style for buttons
//   static TextStyle button(
//       {double size = 14,
//       FontWeight weight = FontWeight.w700,
//       Color? customColor}) {
//     return TextStyle(
//       fontSize: getFontSize(size),
//       fontWeight: weight,
//       color: customColor ?? DynamicColors.buttonText,
//       fontFamily: _fontFamily,
//     );
//   }

//   /// Text style for labels
//   static TextStyle label(
//       {double size = 12,
//       FontWeight weight = FontWeight.w600,
//       Color? customColor}) {
//     return TextStyle(
//       fontSize: getFontSize(size),
//       fontWeight: weight,
//       color: customColor ?? DynamicColors.textSecondary,
//       fontFamily: _fontFamily,
//     );
//   }
// }
