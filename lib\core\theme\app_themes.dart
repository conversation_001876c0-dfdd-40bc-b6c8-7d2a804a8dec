// import 'package:flutter/material.dart';

// import '../utils/app_colors.dart';

// class AppThemes {
//   static final lightTheme = ThemeData(
//     brightness: Brightness.light,
//     primaryColor: AppColors.primaryColor,
//     scaffoldBackgroundColor: AppColors.white,
//     dividerColor: AppColors.secondry,
//     cardColor: AppColors.white,
//     bottomNavigationBarTheme: BottomNavigationBarThemeData(
//       backgroundColor: AppColors.background,
//     ),
//     textSelectionTheme: TextSelectionThemeData(
//       cursorColor: AppColors.primaryColor,
//       selectionColor: AppColors.primaryColor,
//       selectionHandleColor: AppColors.primaryColor,
//     ),
//     colorScheme: ColorScheme.light(
//       primary: AppColors.primaryColor,
//       secondary: AppColors.secondaryColor,
//       surface: AppColors.white,
//       error: AppColors.error,
//     ),
//     appBarTheme: AppBarTheme(
//       backgroundColor: AppColors.background,
//       iconTheme: IconThemeData(color: AppColors.primaryColor),
//       titleTextStyle: TextStyle(color: AppColors.black, fontSize: 20),
//     ),
//     textTheme: TextTheme(
//       bodyLarge: TextStyle(color: AppColors.black),
//       bodyMedium: TextStyle(color: AppColors.black),
//       titleMedium: TextStyle(color: AppColors.black),
//     ),
//   );

//   static final darkTheme = ThemeData(
//     brightness: Brightness.dark,
//     primaryColor: AppColors.darkPrimary,
//     scaffoldBackgroundColor: AppColors.darkBackground,
//     dividerColor: AppColors.darkDivider,
//     cardColor: AppColors.darkCard,
//     bottomNavigationBarTheme: const BottomNavigationBarThemeData(
//       backgroundColor: AppColors.darkSurface,
//     ),
//     textSelectionTheme: const TextSelectionThemeData(
//       cursorColor: AppColors.darkPrimary,
//       selectionColor: AppColors.darkPrimary,
//       selectionHandleColor: AppColors.darkPrimary,
//     ),
//     colorScheme: const ColorScheme.dark(
//       primary: AppColors.darkPrimary,
//       secondary: AppColors.darkSecondary,
//       surface: AppColors.darkSurface,
//       error: AppColors.darkError,
//     ),
//     appBarTheme: const AppBarTheme(
//       backgroundColor: AppColors.darkSurface,
//       iconTheme: IconThemeData(color: AppColors.darkPrimary),
//       titleTextStyle: TextStyle(color: AppColors.darkText, fontSize: 20),
//     ),
//     textTheme: const TextTheme(
//       bodyLarge: TextStyle(color: AppColors.darkText),
//       bodyMedium: TextStyle(color: AppColors.darkText),
//       titleMedium: TextStyle(color: AppColors.darkText),
//     ),
//   );
// }
