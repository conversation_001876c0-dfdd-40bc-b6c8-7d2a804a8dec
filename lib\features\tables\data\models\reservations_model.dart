// To parse this JSON data, do
//
//     final reservationsModel = reservationsModelFromJson(jsonString);

import 'dart:convert';

List<ReservationsModel> reservationsModelFromJson(String str) =>
    List<ReservationsModel>.from(
        json.decode(str).map((x) => ReservationsModel.fromJson(x)));

String reservationsModelToJson(List<ReservationsModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class ReservationsModel {
  final int? id;
  final int? tableId;
  final String? tableCode;
  final int? noOfGusts;
  final String? customerId;
  final String? customerName;
  final String? reservationStarting;
  final String? reservationEnding;
  final DateTime? reservationDate;
  final int? status;
  final String? tenantId;
  final String? companyId;
  final String? branchId;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? createdBy;

  ReservationsModel({
    this.id,
    this.tableId,
    this.tableCode,
    this.noOfGusts,
    this.customerId,
    this.customerName,
    this.reservationStarting,
    this.reservationEnding,
    this.reservationDate,
    this.status,
    this.tenantId,
    this.companyId,
    this.branchId,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
  });

  factory ReservationsModel.fromJson(Map<String, dynamic> json) =>
      ReservationsModel(
        id: json["id"],
        tableId: json["table_id"],
        tableCode: json["table_code"],
        noOfGusts: json["no_of_gusts"],
        customerId: json["customer_id"],
        customerName: json["customer_name"],
        reservationStarting: json["reservation_starting"],
        reservationEnding: json["reservation_ending"],
        reservationDate: json["reservation_date"] == null
            ? null
            : DateTime.parse(json["reservation_date"]),
        status: json["status"],
        tenantId: json["tenant_id"],
        companyId: json["company_id"],
        branchId: json["branch_id"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        createdBy: json["created_by"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "table_id": tableId,
        "table_code": tableCode,
        "no_of_gusts": noOfGusts,
        "customer_id": customerId,
        "customer_name": customerName,
        "reservation_starting": reservationStarting,
        "reservation_ending": reservationEnding,
        "reservation_date":
            "${reservationDate!.year.toString().padLeft(4, '0')}-${reservationDate!.month.toString().padLeft(2, '0')}-${reservationDate!.day.toString().padLeft(2, '0')}",
        "status": status,
        "tenant_id": tenantId,
        "company_id": companyId,
        "branch_id": branchId,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "created_by": createdBy,
      };
}
